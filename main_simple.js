// Simple Tree Model Viewer
console.log('Loading Simple Tree Model Viewer...');

class SimpleTreeViewer {
    constructor() {
        console.log('SimpleTreeViewer constructor called');
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cameraController = null;
        this.treeModel = null;
        this.clock = new THREE.Clock();
        
        this.init();
    }
    
    init() {
        console.log('Initializing SimpleTreeViewer...');
        this.setupScene();
        this.setupLighting();
        this.setupRenderer();
        this.setupCamera();
        this.setupControls();
        this.loadComplexTree();
        this.animate();
        console.log('SimpleTreeViewer initialization complete');
    }
    
    setupScene() {
        console.log('Setting up scene...');
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue background
        
        // Add fog for depth perception
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
        console.log('Scene setup complete');
    }
    
    setupLighting() {
        console.log('Setting up lighting...');
        // Ambient light for overall illumination
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
        console.log('Lighting setup complete');
    }
    
    setupRenderer() {
        console.log('Setting up renderer...');
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Add to DOM
        const container = document.getElementById('container');
        if (container) {
            container.appendChild(this.renderer.domElement);
            console.log('Renderer added to container');
        } else {
            document.body.appendChild(this.renderer.domElement);
            console.log('Renderer added to body');
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
        console.log('Renderer setup complete');
    }
    
    setupCamera() {
        console.log('Setting up camera...');
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(15, 8, 15);
        this.camera.lookAt(0, 6, 0);
        console.log('Camera setup complete. Position:', this.camera.position);
    }
    
    setupControls() {
        console.log('Setting up controls...');
        if (typeof CameraController !== 'undefined') {
            this.cameraController = new CameraController(this.camera, this.renderer.domElement);
            console.log('CameraController initialized');
        } else {
            console.warn('CameraController not available, using basic controls');
            // Basic mouse controls fallback
            this.setupBasicControls();
        }
    }
    
    setupBasicControls() {
        console.log('Setting up basic mouse controls...');
        let isMouseDown = false;
        let mouseX = 0;
        let mouseY = 0;
        
        this.renderer.domElement.addEventListener('mousedown', (event) => {
            isMouseDown = true;
            mouseX = event.clientX;
            mouseY = event.clientY;
        });
        
        this.renderer.domElement.addEventListener('mouseup', () => {
            isMouseDown = false;
        });
        
        this.renderer.domElement.addEventListener('mousemove', (event) => {
            if (!isMouseDown) return;
            
            const deltaX = event.clientX - mouseX;
            const deltaY = event.clientY - mouseY;
            
            // Rotate camera around the tree
            const spherical = new THREE.Spherical();
            spherical.setFromVector3(this.camera.position);
            spherical.theta -= deltaX * 0.01;
            spherical.phi += deltaY * 0.01;
            spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));
            
            this.camera.position.setFromSpherical(spherical);
            this.camera.lookAt(0, 6, 0);
            
            mouseX = event.clientX;
            mouseY = event.clientY;
        });
        
        console.log('Basic controls setup complete');
    }
    
    async loadComplexTree() {
        console.log('Loading complex tree model...');

        try {
            // First, let's check what loaders are available
            console.log('Checking available loaders...');
            console.log('THREE.OBJLoader available:', typeof THREE.OBJLoader !== 'undefined');
            console.log('THREE.MTLLoader available:', typeof THREE.MTLLoader !== 'undefined');

            // Create our own simple OBJ loader if the CDN ones failed
            if (typeof THREE.OBJLoader === 'undefined') {
                console.log('Creating fallback OBJ loader...');
                await this.createFallbackLoaders();
            }

            // Test file accessibility first
            console.log('Testing file accessibility...');
            const objPath = './TREEMODELTEST/Tree.obj';
            const mtlPath = './TREEMODELTEST/Tree.mtl';

            const objResponse = await fetch(objPath);
            const mtlResponse = await fetch(mtlPath);

            console.log('OBJ file status:', objResponse.status, objResponse.ok);
            console.log('MTL file status:', mtlResponse.status, mtlResponse.ok);

            if (!objResponse.ok) {
                throw new Error(`OBJ file not found: ${objResponse.status}`);
            }

            if (!mtlResponse.ok) {
                console.warn(`MTL file not found: ${mtlResponse.status}, proceeding without materials`);
            }

            // Load the actual model
            console.log('Loading OBJ model...');
            const objText = await objResponse.text();
            console.log('OBJ file size:', objText.length, 'characters');
            console.log('OBJ preview:', objText.substring(0, 200));

            // Parse OBJ manually if needed
            const treeModel = await this.parseOBJModel(objText, mtlResponse.ok ? await mtlResponse.text() : null);

            // Add to scene
            this.scene.add(treeModel);
            this.treeModel = treeModel;

            // Position camera to view the model
            this.positionCameraForModel(treeModel);

            console.log('Complex tree model loaded successfully!');
            console.log('Model children:', treeModel.children.length);
            console.log('Scene children count:', this.scene.children.length);

        } catch (error) {
            console.error('Error loading complex tree:', error);
            console.log('Falling back to simple tree...');
            this.createSimpleTree();
        }

        // Hide loading screen
        const loading = document.getElementById('loading');
        if (loading) {
            loading.classList.add('hidden');
            console.log('Loading screen hidden');
        }
    }

    async createFallbackLoaders() {
        console.log('Creating fallback loaders...');
        // We'll implement a simple OBJ parser if the CDN loaders aren't available
        // For now, we'll try to use the built-in loaders or create basic ones
    }

    async parseOBJModel(objText, mtlText) {
        console.log('Parsing OBJ model manually...');

        const geometry = new THREE.BufferGeometry();
        const vertices = [];
        const normals = [];
        const uvs = [];
        const faces = [];

        const lines = objText.split('\n');
        console.log('Processing', lines.length, 'lines from OBJ file...');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line.startsWith('v ')) {
                // Vertex
                const parts = line.split(/\s+/);
                vertices.push(
                    parseFloat(parts[1]),
                    parseFloat(parts[2]),
                    parseFloat(parts[3])
                );
            } else if (line.startsWith('vn ')) {
                // Normal
                const parts = line.split(/\s+/);
                normals.push(
                    parseFloat(parts[1]),
                    parseFloat(parts[2]),
                    parseFloat(parts[3])
                );
            } else if (line.startsWith('f ')) {
                // Face
                const parts = line.split(/\s+/).slice(1);
                for (let j = 0; j < parts.length; j++) {
                    const vertexData = parts[j].split('/');
                    faces.push(parseInt(vertexData[0]) - 1); // OBJ indices are 1-based
                }
            }
        }

        console.log('Parsed vertices:', vertices.length / 3);
        console.log('Parsed faces:', faces.length / 3);

        // Create geometry
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
        if (normals.length > 0) {
            geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
        } else {
            geometry.computeVertexNormals();
        }

        geometry.setIndex(faces);

        // Create material
        const material = new THREE.MeshLambertMaterial({
            color: 0x228B22, // Default green
            side: THREE.DoubleSide
        });

        // Create mesh
        const mesh = new THREE.Mesh(geometry, material);
        mesh.name = 'ComplexTree';

        const group = new THREE.Group();
        group.add(mesh);

        return group;
    }

    positionCameraForModel(model) {
        console.log('Positioning camera for model...');

        // Calculate bounding box
        const box = new THREE.Box3().setFromObject(model);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());

        console.log('Model center:', center);
        console.log('Model size:', size);

        // Position camera to view the entire model
        const maxDim = Math.max(size.x, size.y, size.z);
        const distance = maxDim * 2;

        this.camera.position.set(distance, distance * 0.5, distance);
        this.camera.lookAt(center);

        console.log('Camera positioned at:', this.camera.position);
    }

    createSimpleTree() {
        console.log('Creating simple tree fallback...');

        // Create a tree group
        const treeGroup = new THREE.Group();
        treeGroup.name = 'SimpleTree';

        // Trunk
        const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, 8, 8);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 4;
        treeGroup.add(trunk);

        // Foliage
        const foliageMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const foliage = new THREE.Mesh(new THREE.SphereGeometry(3, 8, 6), foliageMaterial);
        foliage.position.y = 6;
        treeGroup.add(foliage);

        // Add to scene
        this.scene.add(treeGroup);
        this.treeModel = treeGroup;

        console.log('Simple tree fallback created');
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        const deltaTime = this.clock.getDelta();
        
        // Update camera controller if available
        if (this.cameraController && this.cameraController.update) {
            this.cameraController.update(deltaTime);
        }
        
        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing SimpleTreeViewer...');
    
    // Check if THREE.js is available
    if (typeof THREE === 'undefined') {
        console.error('THREE.js not loaded!');
        return;
    }
    
    console.log('THREE.js version:', THREE.REVISION);
    
    // Initialize the viewer
    window.treeViewer = new SimpleTreeViewer();
    console.log('SimpleTreeViewer initialized and stored in window.treeViewer');
});

console.log('Simple Tree Model Viewer script loaded');
