{"name": "tree-model-viewer", "version": "1.0.0", "description": "3D Tree Model Viewer with Three.js and Camera Controller", "main": "main.js", "type": "module", "scripts": {"dev": "python server.py", "serve": "python -m http.server 8000", "start": "python server.py"}, "keywords": ["threejs", "3d", "webgl", "tree", "model", "viewer"], "author": "", "license": "MIT", "dependencies": {"three": "^0.158.0"}, "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": ""}, "bugs": {"url": ""}, "homepage": ""}