<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tree Model Viewer</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #222;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            z-index: 1000;
        }

        #loading.hidden {
            opacity: 0;
            pointer-events: none;
        }

        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            color: white;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <h2>Loading Complex Tree Model...</h2>
            <p>Please wait...</p>
        </div>

        <div id="info">
            <div>Camera: WASD + Mouse</div>
            <div>Loading tree from OBJ file...</div>
        </div>
    </div>

    <!-- Three.js Core -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r152/three.min.js"></script>
    
    <script>
        // Simple initialization
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded, starting tree viewer...');
            
            // Load our main script
            const script = document.createElement('script');
            script.src = 'tree_viewer.js';
            script.onload = () => {
                console.log('Tree viewer script loaded');
                if (typeof TreeViewer !== 'undefined') {
                    window.viewer = new TreeViewer();
                } else {
                    console.error('TreeViewer not available');
                }
            };
            script.onerror = () => {
                console.error('Failed to load tree viewer script');
            };
            document.head.appendChild(script);
        });
    </script>
</body>
</html>
