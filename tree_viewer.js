class TreeViewer {
    constructor() {
        console.log('<PERSON>Viewer initializing...');
        this.init();
    }

    init() {
        // Create scene, camera, renderer
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB); // Sky blue
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        document.getElementById('container').appendChild(this.renderer.domElement);

        // Add lights
        this.setupLights();
        
        // Setup camera controls
        this.setupControls();
        
        // Load the tree model
        this.loadTree();
        
        // Start render loop
        this.animate();
        
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    setupLights() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
    }

    setupControls() {
        // Simple mouse controls
        this.mouse = { x: 0, y: 0 };
        this.isMouseDown = false;
        this.cameraRotation = { x: 0, y: 0 };
        
        document.addEventListener('mousedown', (e) => {
            this.isMouseDown = true;
            this.mouse.x = e.clientX;
            this.mouse.y = e.clientY;
        });
        
        document.addEventListener('mouseup', () => {
            this.isMouseDown = false;
        });
        
        document.addEventListener('mousemove', (e) => {
            if (this.isMouseDown) {
                const deltaX = e.clientX - this.mouse.x;
                const deltaY = e.clientY - this.mouse.y;
                
                this.cameraRotation.y += deltaX * 0.01;
                this.cameraRotation.x += deltaY * 0.01;
                
                this.mouse.x = e.clientX;
                this.mouse.y = e.clientY;
                
                this.updateCamera();
            }
        });
        
        // Keyboard controls
        this.keys = {};
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // Initial camera position
        this.cameraDistance = 20;
        this.updateCamera();
    }

    updateCamera() {
        const x = Math.sin(this.cameraRotation.y) * this.cameraDistance;
        const z = Math.cos(this.cameraRotation.y) * this.cameraDistance;
        const y = Math.sin(this.cameraRotation.x) * this.cameraDistance;
        
        this.camera.position.set(x, y + 10, z);
        this.camera.lookAt(0, 5, 0);
    }

    async loadTree() {
        console.log('Loading tree model...');
        
        try {
            // Try to load the OBJ file
            const objPath = './TREEMODELTEST/Tree.obj';
            console.log('Attempting to load:', objPath);
            
            const response = await fetch(objPath);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const objText = await response.text();
            console.log('OBJ file loaded, size:', objText.length, 'characters');
            console.log('First 200 characters:', objText.substring(0, 200));
            
            // Parse the OBJ file manually
            const treeModel = this.parseOBJ(objText);
            
            // Add to scene
            this.scene.add(treeModel);
            console.log('Tree model added to scene');
            
            // Hide loading screen
            document.getElementById('loading').classList.add('hidden');
            
        } catch (error) {
            console.error('Error loading tree:', error);
            console.log('Creating fallback simple tree...');
            this.createSimpleTree();
            document.getElementById('loading').classList.add('hidden');
        }
    }

    parseOBJ(objText) {
        console.log('Parsing OBJ file...');
        
        const vertices = [];
        const faces = [];
        const lines = objText.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            if (line.startsWith('v ')) {
                // Vertex
                const parts = line.split(/\s+/);
                vertices.push(
                    parseFloat(parts[1]),
                    parseFloat(parts[2]),
                    parseFloat(parts[3])
                );
            } else if (line.startsWith('f ')) {
                // Face
                const parts = line.split(/\s+/).slice(1);
                
                // Convert to triangles (assuming quads or triangles)
                if (parts.length >= 3) {
                    for (let j = 1; j < parts.length - 1; j++) {
                        faces.push(
                            parseInt(parts[0].split('/')[0]) - 1,
                            parseInt(parts[j].split('/')[0]) - 1,
                            parseInt(parts[j + 1].split('/')[0]) - 1
                        );
                    }
                }
            }
        }
        
        console.log('Parsed vertices:', vertices.length / 3);
        console.log('Parsed faces:', faces.length / 3);
        
        // Create geometry
        const geometry = new THREE.BufferGeometry();
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
        geometry.setIndex(faces);
        geometry.computeVertexNormals();
        
        // Create material
        const material = new THREE.MeshLambertMaterial({ 
            color: 0x8B4513, // Brown
            side: THREE.DoubleSide 
        });
        
        // Create mesh
        const mesh = new THREE.Mesh(geometry, material);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        
        // Create group and add mesh
        const group = new THREE.Group();
        group.add(mesh);
        
        return group;
    }

    createSimpleTree() {
        console.log('Creating simple tree fallback...');
        
        const group = new THREE.Group();
        
        // Trunk
        const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, 8, 8);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 4;
        trunk.castShadow = true;
        group.add(trunk);
        
        // Foliage
        const foliageGeometry = new THREE.SphereGeometry(3, 8, 6);
        const foliageMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const foliage = new THREE.Mesh(foliageGeometry, foliageMaterial);
        foliage.position.y = 7;
        foliage.castShadow = true;
        group.add(foliage);
        
        // Ground
        const groundGeometry = new THREE.PlaneGeometry(50, 50);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);
        
        this.scene.add(group);
        console.log('Simple tree created');
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        
        // Handle keyboard movement
        if (this.keys['KeyW']) this.cameraDistance = Math.max(5, this.cameraDistance - 0.5);
        if (this.keys['KeyS']) this.cameraDistance = Math.min(50, this.cameraDistance + 0.5);
        
        this.updateCamera();
        this.renderer.render(this.scene, this.camera);
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
}
