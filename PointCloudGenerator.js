class PointCloudGenerator {
    constructor() {
        this.textureCache = new Map();
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.canvas.width = 512;
        this.canvas.height = 512;
        this.progressCallback = null;
    }

    setProgressCallback(callback) {
        this.progressCallback = callback;
    }

    updateProgress(progress, message) {
        if (this.progressCallback) {
            this.progressCallback(progress, message);
        }
    }

    async generatePointCloud(objPath, mtlPath, targetPoints = 200000) {
        console.log('Starting pointcloud generation...');

        try {
            this.updateProgress(0, 'Loading tree model...');

            // Load the tree model first
            const { geometry, materials } = await this.loadTreeModel(objPath, mtlPath);
            this.updateProgress(30, 'Model loaded, generating points...');

            // Generate points using combined sampling
            const points = await this.generatePoints(geometry, materials, targetPoints);
            this.updateProgress(80, 'Creating point cloud mesh...');

            // Create THREE.js point cloud
            const pointCloud = this.createPointCloudMesh(points);
            this.updateProgress(100, 'Complete!');

            console.log(`Generated pointcloud with ${points.length} points`);
            return pointCloud;

        } catch (error) {
            console.error('Error generating pointcloud:', error);
            this.updateProgress(0, `Error: ${error.message}`);
            throw error;
        }
    }

    async loadTreeModel(objPath, mtlPath) {
        const mtlLoader = new THREE.MTLLoader();
        const objLoader = new THREE.OBJLoader();

        // Set paths
        const basePath = './TREEMODELTEST/';
        mtlLoader.setPath(basePath);

        this.updateProgress(5, 'Loading materials...');

        // Load materials
        console.log('Attempting to load MTL file...');
        const materials = await new Promise((resolve, reject) => {
            mtlLoader.load('Tree.mtl',
                (loadedMaterials) => {
                    console.log('MTL loaded successfully:', loadedMaterials);
                    resolve(loadedMaterials);
                },
                (progress) => {
                    console.log('MTL loading progress:', progress);
                },
                (error) => {
                    console.error('Failed to load MTL:', error);
                    reject(new Error(`Failed to load materials: ${error.message || error}`));
                }
            );
        });

        materials.preload();
        this.updateProgress(15, 'Loading geometry...');

        // Load geometry
        objLoader.setMaterials(materials);
        objLoader.setPath(basePath);

        console.log('Attempting to load OBJ file...');
        const object = await new Promise((resolve, reject) => {
            objLoader.load('Tree.obj',
                (loadedObject) => {
                    console.log('OBJ loaded successfully:', loadedObject);
                    resolve(loadedObject);
                },
                (progress) => {
                    console.log('OBJ loading progress:', progress);
                },
                (error) => {
                    console.error('Failed to load OBJ:', error);
                    reject(new Error(`Failed to load geometry: ${error.message || error}`));
                }
            );
        });
        
        // Extract geometry and materials
        const geometries = [];
        const materialMap = new Map();

        console.log('Processing loaded object:', object);

        object.traverse((child) => {
            console.log('Found child:', child.type, child.name);
            if (child.isMesh) {
                console.log('Mesh found:', {
                    name: child.name,
                    vertices: child.geometry.attributes.position.count,
                    material: child.material ? child.material.name : 'no material'
                });

                geometries.push({
                    geometry: child.geometry,
                    material: child.material,
                    matrix: child.matrixWorld.clone()
                });

                if (child.material && child.material.map) {
                    materialMap.set(child.material.name || 'default', child.material);
                }
            }
        });

        console.log(`Found ${geometries.length} geometries`);

        if (geometries.length === 0) {
            throw new Error('No mesh geometries found in the loaded model');
        }

        return { geometry: geometries, materials: materialMap };
    }

    async generatePoints(geometries, materials, targetPoints) {
        const points = [];

        console.log(`Generating ${targetPoints} points from ${geometries.length} geometries`);

        // Calculate surface area for each geometry to distribute points proportionally
        const surfaceAreas = geometries.map(({ geometry }) => this.calculateSurfaceArea(geometry));
        const totalSurfaceArea = surfaceAreas.reduce((sum, area) => sum + area, 0);

        console.log('Surface areas:', surfaceAreas, 'Total:', totalSurfaceArea);

        // 70% surface sampling, 30% volume sampling
        const surfacePoints = Math.floor(targetPoints * 0.7);
        const volumePoints = targetPoints - surfacePoints;

        console.log(`Surface points: ${surfacePoints}, Volume points: ${volumePoints}`);

        this.updateProgress(35, `Generating ${surfacePoints} surface points...`);

        // Generate surface points
        for (let i = 0; i < geometries.length; i++) {
            const { geometry, material, matrix } = geometries[i];
            const pointsForThisGeometry = Math.floor((surfaceAreas[i] / totalSurfaceArea) * surfacePoints);

            console.log(`Generating ${pointsForThisGeometry} points for geometry ${i}`);

            const surfacePts = await this.generateSurfacePoints(geometry, material, matrix, pointsForThisGeometry);
            points.push(...surfacePts);

            this.updateProgress(35 + (i + 1) / geometries.length * 30, `Generated ${points.length} surface points...`);
        }

        this.updateProgress(65, `Generating ${volumePoints} volume points...`);

        // Generate volume points
        const boundingBox = this.calculateBoundingBox(geometries);
        const volumePts = this.generateVolumePoints(geometries, boundingBox, volumePoints);
        points.push(...volumePts);

        console.log(`Generated total of ${points.length} points`);

        return points;
    }

    calculateSurfaceArea(geometry) {
        const positions = geometry.attributes.position.array;
        const indices = geometry.index ? geometry.index.array : null;
        let area = 0;
        
        if (indices) {
            for (let i = 0; i < indices.length; i += 3) {
                const a = new THREE.Vector3().fromArray(positions, indices[i] * 3);
                const b = new THREE.Vector3().fromArray(positions, indices[i + 1] * 3);
                const c = new THREE.Vector3().fromArray(positions, indices[i + 2] * 3);
                
                const ab = b.clone().sub(a);
                const ac = c.clone().sub(a);
                area += ab.cross(ac).length() * 0.5;
            }
        } else {
            for (let i = 0; i < positions.length; i += 9) {
                const a = new THREE.Vector3().fromArray(positions, i);
                const b = new THREE.Vector3().fromArray(positions, i + 3);
                const c = new THREE.Vector3().fromArray(positions, i + 6);
                
                const ab = b.clone().sub(a);
                const ac = c.clone().sub(a);
                area += ab.cross(ac).length() * 0.5;
            }
        }
        
        return area;
    }

    async generateSurfacePoints(geometry, material, matrix, numPoints) {
        const points = [];
        const positions = geometry.attributes.position.array;
        const normals = geometry.attributes.normal ? geometry.attributes.normal.array : null;
        const uvs = geometry.attributes.uv ? geometry.attributes.uv.array : null;
        const indices = geometry.index ? geometry.index.array : null;
        
        // Load texture if available
        let textureData = null;
        if (material && material.map && material.map.image) {
            textureData = await this.getTextureData(material.map.image);
        }
        
        const triangleCount = indices ? indices.length / 3 : positions.length / 9;
        
        for (let i = 0; i < numPoints; i++) {
            // Random triangle selection
            const triangleIndex = Math.floor(Math.random() * triangleCount);
            
            let a, b, c, uvA, uvB, uvC;
            
            if (indices) {
                const i1 = indices[triangleIndex * 3];
                const i2 = indices[triangleIndex * 3 + 1];
                const i3 = indices[triangleIndex * 3 + 2];
                
                a = new THREE.Vector3().fromArray(positions, i1 * 3);
                b = new THREE.Vector3().fromArray(positions, i2 * 3);
                c = new THREE.Vector3().fromArray(positions, i3 * 3);
                
                if (uvs) {
                    uvA = new THREE.Vector2().fromArray(uvs, i1 * 2);
                    uvB = new THREE.Vector2().fromArray(uvs, i2 * 2);
                    uvC = new THREE.Vector2().fromArray(uvs, i3 * 2);
                }
            } else {
                const baseIndex = triangleIndex * 9;
                a = new THREE.Vector3().fromArray(positions, baseIndex);
                b = new THREE.Vector3().fromArray(positions, baseIndex + 3);
                c = new THREE.Vector3().fromArray(positions, baseIndex + 6);
                
                if (uvs) {
                    const uvBase = triangleIndex * 6;
                    uvA = new THREE.Vector2().fromArray(uvs, uvBase);
                    uvB = new THREE.Vector2().fromArray(uvs, uvBase + 2);
                    uvC = new THREE.Vector2().fromArray(uvs, uvBase + 4);
                }
            }
            
            // Random barycentric coordinates
            let r1 = Math.random();
            let r2 = Math.random();
            if (r1 + r2 > 1) {
                r1 = 1 - r1;
                r2 = 1 - r2;
            }
            const r3 = 1 - r1 - r2;
            
            // Interpolate position
            const position = new THREE.Vector3()
                .addScaledVector(a, r1)
                .addScaledVector(b, r2)
                .addScaledVector(c, r3);
            
            // Apply transformation matrix
            position.applyMatrix4(matrix);
            
            // Get color from texture or use default
            let color = new THREE.Color(0x4a7c59); // Default green
            
            if (textureData && uvA && uvB && uvC) {
                // Interpolate UV coordinates
                const uv = new THREE.Vector2()
                    .addScaledVector(uvA, r1)
                    .addScaledVector(uvB, r2)
                    .addScaledVector(uvC, r3);
                
                color = this.sampleTexture(textureData, uv);
            }
            
            // Add some color variation for realism
            color = this.addColorVariation(color);
            
            points.push({
                position: position,
                color: color
            });
        }
        
        return points;
    }

    generateVolumePoints(geometries, boundingBox, numPoints) {
        const points = [];
        const { min, max } = boundingBox;

        // Simplified approach: generate points within a smaller bounding box
        // This assumes the tree has a roughly cylindrical/conical shape
        const center = new THREE.Vector3().addVectors(min, max).multiplyScalar(0.5);
        const size = new THREE.Vector3().subVectors(max, min);

        for (let i = 0; i < numPoints; i++) {
            let position;
            let isValid = false;
            let attempts = 0;

            // Try to find a valid interior point
            while (!isValid && attempts < 20) {
                // Generate point with bias toward center and trunk
                const heightRatio = Math.random();
                const radiusAtHeight = this.getTreeRadiusAtHeight(heightRatio, size);

                // Random angle around trunk
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * radiusAtHeight * 0.8; // 80% of max radius for interior

                position = new THREE.Vector3(
                    center.x + Math.cos(angle) * radius,
                    min.y + heightRatio * size.y,
                    center.z + Math.sin(angle) * radius
                );

                // Simple validation - check if within reasonable bounds
                isValid = position.x >= min.x && position.x <= max.x &&
                         position.y >= min.y && position.y <= max.y &&
                         position.z >= min.z && position.z <= max.z;

                attempts++;
            }

            if (isValid) {
                // Generate interior color based on position
                let color = this.generateInteriorColor(position, boundingBox);
                color = this.addColorVariation(color);

                points.push({
                    position: position,
                    color: color
                });
            }
        }

        return points;
    }

    getTreeRadiusAtHeight(heightRatio, size) {
        // Tree gets narrower toward the top
        // Base radius is proportional to tree width, tapering to 10% at top
        const baseRadius = Math.min(size.x, size.z) * 0.4;
        const topRadius = baseRadius * 0.1;

        // Smooth transition from base to top
        const t = Math.pow(1 - heightRatio, 0.7); // Slightly curved taper
        return THREE.MathUtils.lerp(topRadius, baseRadius, t);
    }

    generateInteriorColor(position, boundingBox) {
        const { min, max } = boundingBox;

        // Height-based coloring
        const heightRatio = (position.y - min.y) / (max.y - min.y);

        if (heightRatio < 0.3) {
            // Lower trunk - darker brown
            return new THREE.Color(0x3d2914);
        } else if (heightRatio < 0.7) {
            // Mid trunk - medium brown
            return new THREE.Color(0x5d4037);
        } else {
            // Upper areas - green tones
            return new THREE.Color(0x2e5233);
        }
    }

    addColorVariation(baseColor) {
        const variation = 0.15; // 15% variation
        const r = Math.max(0, Math.min(1, baseColor.r + (Math.random() - 0.5) * variation));
        const g = Math.max(0, Math.min(1, baseColor.g + (Math.random() - 0.5) * variation));
        const b = Math.max(0, Math.min(1, baseColor.b + (Math.random() - 0.5) * variation));

        return new THREE.Color(r, g, b);
    }

    calculateBoundingBox(geometries) {
        const box = new THREE.Box3();

        for (const { geometry, matrix } of geometries) {
            const tempGeometry = geometry.clone();
            tempGeometry.applyMatrix4(matrix);

            const geometryBox = new THREE.Box3().setFromBufferAttribute(
                tempGeometry.attributes.position
            );
            box.union(geometryBox);
        }

        return box;
    }

    async getTextureData(image) {
        const cacheKey = image.src || image.currentSrc;

        if (this.textureCache.has(cacheKey)) {
            return this.textureCache.get(cacheKey);
        }

        // Draw image to canvas
        this.canvas.width = image.width || 512;
        this.canvas.height = image.height || 512;
        this.ctx.drawImage(image, 0, 0);

        // Get image data
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);

        const textureData = {
            data: imageData.data,
            width: this.canvas.width,
            height: this.canvas.height
        };

        this.textureCache.set(cacheKey, textureData);
        return textureData;
    }

    sampleTexture(textureData, uv) {
        const x = Math.floor(uv.x * textureData.width) % textureData.width;
        const y = Math.floor((1 - uv.y) * textureData.height) % textureData.height;

        const index = (y * textureData.width + x) * 4;

        const r = textureData.data[index] / 255;
        const g = textureData.data[index + 1] / 255;
        const b = textureData.data[index + 2] / 255;

        return new THREE.Color(r, g, b);
    }

    createPointCloudMesh(points) {
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(points.length * 3);
        const colors = new Float32Array(points.length * 3);
        const sizes = new Float32Array(points.length);

        for (let i = 0; i < points.length; i++) {
            const point = points[i];

            positions[i * 3] = point.position.x;
            positions[i * 3 + 1] = point.position.y;
            positions[i * 3 + 2] = point.position.z;

            colors[i * 3] = point.color.r;
            colors[i * 3 + 1] = point.color.g;
            colors[i * 3 + 2] = point.color.b;

            // Vary point sizes slightly for more realistic look
            sizes[i] = 0.03 + Math.random() * 0.04; // 0.03 to 0.07
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        // Create enhanced point material for better visual quality
        const material = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            sizeAttenuation: true,
            alphaTest: 0.1,
            transparent: false,
            blending: THREE.NormalBlending,
            depthWrite: true,
            depthTest: true
        });

        const pointCloud = new THREE.Points(geometry, material);
        pointCloud.name = 'TreePointCloud';

        // Enable frustum culling for better performance
        pointCloud.frustumCulled = true;

        return pointCloud;
    }

    // Utility method to adjust point cloud density dynamically
    createLODPointCloud(points, maxPoints = 200000) {
        if (points.length <= maxPoints) {
            return this.createPointCloudMesh(points);
        }

        // Create multiple LOD levels
        const lod = new THREE.LOD();

        // High detail (close up)
        const highDetail = this.createPointCloudMesh(points);
        lod.addLevel(highDetail, 0);

        // Medium detail
        const mediumPoints = this.subsamplePoints(points, Math.floor(maxPoints * 0.6));
        const mediumDetail = this.createPointCloudMesh(mediumPoints);
        lod.addLevel(mediumDetail, 20);

        // Low detail (far away)
        const lowPoints = this.subsamplePoints(points, Math.floor(maxPoints * 0.3));
        const lowDetail = this.createPointCloudMesh(lowPoints);
        lod.addLevel(lowDetail, 50);

        return lod;
    }

    subsamplePoints(points, targetCount) {
        if (points.length <= targetCount) return points;

        const step = points.length / targetCount;
        const subsampled = [];

        for (let i = 0; i < points.length; i += step) {
            subsampled.push(points[Math.floor(i)]);
        }

        return subsampled.slice(0, targetCount);
    }
}
