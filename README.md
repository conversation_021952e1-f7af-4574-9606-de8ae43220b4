# 🌳 3D Tree Model Viewer

A Three.js-based 3D tree model viewer with advanced camera controls for exploring OBJ models with textures.

## 🚀 Quick Start

### Option 1: Python Server (Recommended)
```bash
python server.py
```

### Option 2: Simple Python HTTP Server
```bash
python -m http.server 8000
```

Then open your browser to `http://localhost:8000`

## 📋 Camera Controls

- **Mouse**: Click and drag to look around
- **WASD**: Move forward/back/left/right
- **Space/Shift**: Move up/down
- **Q/E**: Rotate left/right
- **Mouse Wheel**: Zoom in/out
- **R**: Reset camera position

## 📁 Project Structure

```
├── index.html              # Main HTML entry point
├── main.js                 # Three.js application logic
├── CameraController.js     # Advanced camera controller
├── server.py              # Python HTTP server with CORS support
├── package.json           # Project configuration
└── TREEMODELTEST/         # 3D model assets
    ├── Tree.obj           # 3D model geometry
    ├── Tree.mtl           # Material definitions
    ├── bark_0004.jpg      # Bark texture
    ├── DB2X2_L01.png      # Leaf texture 1
    ├── DB2X2_L02.png      # Leaf texture 2
    └── DB2X2_L02_NRM.png  # Normal map
```

## 🔧 Features

- **Full OBJ/MTL Support**: Loads 3D models with materials and textures
- **Advanced Camera**: First-person style camera with smooth movement
- **Real-time Stats**: FPS, triangle count, and camera position display
- **CORS-Free**: Python server eliminates cross-origin issues
- **Responsive**: Adapts to window resizing
- **Performance Optimized**: Efficient rendering with shadow support

## 🛠️ Technical Details

- **Three.js**: 3D graphics library
- **ES6 Modules**: Modern JavaScript module system
- **WebGL**: Hardware-accelerated 3D rendering
- **Shadow Mapping**: Realistic lighting and shadows
- **Fog Effects**: Atmospheric depth perception

## 🎯 Next Steps for Point Cloud Conversion

This setup provides the foundation for converting the tree model to a point cloud:

1. **Extract Vertices**: Use `geometry.attributes.position` to get vertex data
2. **Point Cloud Material**: Replace mesh materials with `THREE.PointsMaterial`
3. **Particle System**: Convert mesh to `THREE.Points` object
4. **Custom Shaders**: Implement advanced point cloud effects

## 🐛 Troubleshooting

- **Model not loading**: Ensure all texture files are in the TREEMODELTEST folder
- **CORS errors**: Use the Python server instead of opening HTML directly
- **Performance issues**: Check the stats panel for triangle count and FPS

## 📝 License

MIT License - Feel free to modify and use for your projects!
